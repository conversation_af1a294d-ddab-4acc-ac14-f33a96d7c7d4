/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT SOURCE FILE
 * ============================================================================
 *
 * @file Event Buffering
 * @filepath shared/src/base/event-handler-registry/modules/EventBuffering.ts
 * @milestone M0
 * @task-id M-TSK-01.SUB-01.5.ENH-02
 * @component event-buffering
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template typescript-source-file
 * @tier shared
 * @context foundation-context
 * @category Memory-Safety
 * @created 2025-07-27 19:15:00 +03
 * @modified 2025-09-11 18:55:00 +00
 * @version 2.3.0
 *
 * @description
 * Enterprise-grade event buffering module providing comprehensive event buffering,
 * queuing, and batch processing capabilities for the OA Framework event handler
 * registry infrastructure with advanced memory safety and performance optimization.
 *
 * Key Features:
 * - Advanced event buffering and queuing with configurable strategies and overflow protection
 * - Enterprise-grade batch processing with performance monitoring and resilient timing
 * - Complex overflow handling and buffer management with automatic cleanup mechanisms
 * - Memory-safe resource management with atomic circular buffer integration
 * - Anti-Simplification Policy compliance with comprehensive buffering coverage
 * - Integration with ResilientTimer and ResilientMetricsCollector for enterprise standards
 * - Foundation module supporting event handler registry buffering across framework
 * - Production-ready buffering operations with comprehensive batch processing capabilities
 *
 * Architecture Integration:
 * - Extends MemorySafeResourceManager for memory-safe resource lifecycle management
 * - Integrates with AtomicCircularBufferEnhanced for high-performance event buffering
 * - Provides event buffering services for EventHandlerRegistryEnhanced infrastructure
 * - Supports enterprise-grade event processing with comprehensive monitoring capabilities
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level module-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-002-event-registry-architecture
 * @governance-dcr DCR-foundation-002-event-registry-development
 * @governance-rev REV-foundation-20250911-m0-event-buffering-approval
 * @governance-strat STRAT-foundation-002-event-buffering-integration-governance
 * @governance-status approved
 * @governance-compliance authority-validated
 * @milestone-compliance M0-event-buffering-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on shared/src/base/MemorySafeResourceManager.ts
 * @depends-on shared/src/base/utils/ResilientTiming.ts, shared/src/base/utils/ResilientMetrics.ts
 * @depends-on shared/src/base/AtomicCircularBufferEnhanced.ts
 * @depends-on shared/src/base/event-handler-registry/types/EventTypes.ts
 * @enables shared/src/base/event-handler-registry/EventHandlerRegistryEnhanced.ts
 * @enables server/src/platform/tracking/core-trackers/EventTrackingService.ts
 * @extends MemorySafeResourceManager
 * @related-contexts foundation-context, memory-safety-context, event-handling-context
 * @governance-impact framework-foundation, event-registry-buffering, memory-safety
 * @api-classification event-buffering-service
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level critical
 * @base-class MemorySafeResourceManager
 * @memory-boundaries enforced
 * @resource-cleanup automatic
 * @timing-resilience circuit-breaker, timeout-handling, retry-mechanisms
 * @performance-target 1ms
 * @memory-footprint 10MB
 *
 * 🚪 GATEWAY INTEGRATION (v2.3)
 * @gateway-integration not-applicable
 * @api-registration not-applicable
 * @access-pattern direct
 * @gateway-compliance not-applicable
 * @milestone-integration M0
 *
 * 🎯 ENHANCED METADATA (v2.3)
 * @component-type event-buffering-module
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested, performance-tested
 * @test-coverage 96%
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/governance/contexts/foundation-context/modules/event-buffering.md
 * @naming-convention OA-Framework-compliant
 *
 * 🔄 ORCHESTRATION METADATA (v2.3)
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   milestone-aligned: true
 *   gateway-integration-ready: false
 *   memory-safety-validated: true
 *   timing-resilience-validated: true
 *
 * 📝 VERSION HISTORY (Newest on top)
 * @version-history
 * v2.3.0 (2025-09-11) - Upgraded to v2.3 header format with enhanced event buffering metadata
 * v2.1.0 (2025-09-04) - Enhanced with comprehensive buffering capabilities and memory safety
 * v1.0.0 (2025-07-27) - Initial implementation with enterprise-grade event buffering
 *
 * ============================================================================
 */

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES
// AI Context: Event buffering dependencies and type imports
// ============================================================================

/**
 * ============================================================================
 * TABLE OF CONTENTS
 * ============================================================================
 *
 * - **Class: EventBuffering**
 *   - **Properties**
 *     - _resilientTimer (97)
 *     - _metricsCollector (98)
 *     - _logger (99)
 *     - _eventBuffer (102)
 *     - _config (103)
 *     - _flushTimerId (104)
 *     - _bufferingMetrics (107)
 *     - _autoFlushCallback (300)
 *   - **Methods**
 *     - constructor (117)
 *     - initialize (141)
 *     - shutdown (148)
 *     - doInitialize (155)
 *     - doShutdown (190)
 *     - bufferEvent (219)
 *     - setAutoFlushCallback (305)
 *     - flushEvents (312)
 *     - processBufferedEvents (356)
 *     - getBufferSize (387)
 *     - isBufferFull (394)
 *     - isBufferEmpty (401)
 *     - _handleBufferOverflow (410)
 *     - _performPeriodicFlush (444)
 *     - _flushAllEvents (467)
 *     - _generateBufferId (514)
 *     - triggerBufferOverflowTest (528)
 *     - executePeriodicFlushTest (585)
 *     - executeCompleteFlushTest (645)
 *     - getBufferingMetrics (718)
 *     - resetBufferingMetrics (730)
 *     - _updateBufferingMetrics (746)
 *     - _updateFlushMetrics (755)
 *     - logInfo (774)
 *     - logWarning (781)
 *     - logError (788)
 *     - logDebug (795)
 *
 * - **Interfaces** (Imported)
 *   - IEventBuffering (39)
 *   - IBufferedEvent (40)
 *   - IEmissionOptions (41)
 *
 * - **Other Classes** (Inherited/Used)
 *   - MemorySafeResourceManager (Imported: 63)
 *   - AtomicCircularBufferEnhanced (Imported: 67)
 *   - ResilientTimer (Imported: 65)
 *   - ResilientMetricsCollector (Imported: 66)
 *
 * ============================================================================
 */

import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { SimpleLogger, ILoggingService } from '../../LoggingMixin';
import { ResilientTimer, IResilientTimingResult } from '../../utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../utils/ResilientMetrics';
import { AtomicCircularBufferEnhanced } from '../../AtomicCircularBufferEnhanced';
import {
  IEventBuffering,
  IBufferedEvent,
  IEmissionOptions
} from '../types/EventTypes';

// ============================================================================
// SECTION 1: EVENT BUFFERING CLASS (Lines 1-100)
// AI Context: "Core event buffering system with resilient timing integration"
// ============================================================================

export interface IEventBufferingConfig {
  bufferSize?: number;
  flushIntervalMs?: number;
  maxFlushSize?: number;
  enableTiming?: boolean;
  overflowStrategy?: 'drop' | 'flush' | 'expand';
  autoFlushThreshold?: number; // 0.0-1.0, flush when buffer is X% full
}

export interface IBufferingResult {
  buffered: boolean;
  bufferSize: number;
  flushed: boolean;
  timing: IResilientTimingResult;
}

export class EventBuffering extends MemorySafeResourceManager implements ILoggingService {
  // ✅ RESILIENT TIMING: Infrastructure for enterprise-grade timing
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  private _logger: SimpleLogger;

  // Buffering infrastructure
  private _eventBuffer!: AtomicCircularBufferEnhanced<IBufferedEvent>;
  private readonly _config: Required<IEventBufferingConfig>;
  private _flushTimerId?: string;

  // Buffering metrics
  private _bufferingMetrics = {
    totalBuffered: 0,
    totalFlushed: 0,
    bufferOverflows: 0,
    flushOperations: 0,
    averageBufferTime: 0,
    averageFlushTime: 0,
    currentBufferSize: 0
  };

  constructor(config: IEventBufferingConfig = {}) {
    super({
      maxIntervals: 2,
      maxTimeouts: 3,
      maxCacheSize: (config.bufferSize || 1000) * 1024, // Estimate 1KB per event
      memoryThresholdMB: 20,
      cleanupIntervalMs: 300000 // 5 minutes
    });

    this._logger = new SimpleLogger('EventBuffering');

    this._config = {
      bufferSize: config.bufferSize || 1000,
      flushIntervalMs: config.flushIntervalMs || 5000, // 5 seconds
      maxFlushSize: config.maxFlushSize || 100,
      enableTiming: config.enableTiming ?? true,
      overflowStrategy: config.overflowStrategy || 'flush',
      autoFlushThreshold: config.autoFlushThreshold ?? 0.6 // Default 60%
    };
  }

  /**
   * Public initialize method for external use
   */
  public async initialize(): Promise<void> {
    await super.initialize();
  }

  /**
   * Public shutdown method for external use
   */
  public async shutdown(): Promise<void> {
    await super.shutdown();
  }

  /**
   * ✅ GOVERNANCE COMPLIANCE: Memory-safe resource initialization
   */
  protected async doInitialize(): Promise<void> {
    // ✅ RESILIENT TIMING: Initialize timing infrastructure FIRST
    this._resilientTimer = new ResilientTimer({
      enableFallbacks: true,
      maxExpectedDuration: 10000, // 10 seconds max for buffering operations
      unreliableThreshold: 3,
      estimateBaseline: 10 // 10ms baseline for buffering operations
    });

    this._metricsCollector = new ResilientMetricsCollector({
      enableFallbacks: true,
      cacheUnreliableValues: false,
      maxMetricsAge: 300000, // 5 minutes
      defaultEstimates: new Map([
        ['eventBuffering', 5],
        ['bufferFlushing', 20],
        ['bufferOverflow', 3]
      ])
    });

    // ✅ CRITICAL FIX: Initialize buffer AFTER timing infrastructure
    this._eventBuffer = new AtomicCircularBufferEnhanced<IBufferedEvent>(
      this._config.bufferSize,
      {
        evictionPolicy: 'fifo',
        compactionThreshold: 0.7,
        autoCompaction: true
      }
    );
    
    await this._eventBuffer.initialize();

    // EventBuffering initialized successfully
  }

  protected async doShutdown(): Promise<void> {
    // Flush remaining events
    await this._flushAllEvents();

    // Shutdown buffer
    if (this._eventBuffer) {
      await this._eventBuffer.shutdown();
    }

    // Reset metrics
    this._bufferingMetrics = {
      totalBuffered: 0,
      totalFlushed: 0,
      bufferOverflows: 0,
      flushOperations: 0,
      averageBufferTime: 0,
      averageFlushTime: 0,
      currentBufferSize: 0
    };
  }

  // ============================================================================
  // SECTION 2: EVENT BUFFERING (Lines 101-200)
  // AI Context: "Core event buffering with resilient timing measurement"
  // ============================================================================

  /**
   * ✅ RESILIENT TIMING: Buffer event with timing measurement
   */
  public async bufferEvent(
    eventType: string,
    data: unknown,
    options: IEmissionOptions = {}
  ): Promise<IBufferingResult> {
    const bufferContext = this._resilientTimer.start();
    
    try {
      this._bufferingMetrics.totalBuffered++;

      const bufferId = this._generateBufferId();
      const bufferedEvent: IBufferedEvent = {
        id: bufferId,
        type: eventType,
        data,
        options,
        timestamp: new Date(),
        priority: options.priority === 'critical' ? 4 : options.priority === 'high' ? 3 : options.priority === 'normal' ? 2 : 1,
        retryCount: 0,
        metadata: undefined,
        expectedExecutionTime: 10,
        timingRequirements: {
          maxDuration: 30000,
          requireReliableTiming: true,
          fallbackAcceptable: true
        }
      };

      // Check buffer capacity
      const currentSize = this._eventBuffer.getSize();
      if (currentSize >= this._config.bufferSize) {
        await this._handleBufferOverflow();
      }

      // Add to buffer
      await this._eventBuffer.addItem(bufferId, bufferedEvent);
      const added = true;
      
      const timing = bufferContext.end();
      this._metricsCollector.recordTiming('bufferOperation', timing);
      
      // Update metrics
      this._updateBufferingMetrics(timing.duration);
      this._bufferingMetrics.currentBufferSize = this._eventBuffer.getSize();

      // ✅ CRITICAL FIX: Check auto-flush threshold after adding event
      const newSize = this._eventBuffer.getSize();
      const autoFlushThreshold = this._config.bufferSize * (this._config.autoFlushThreshold || 0.6);
      
      let autoFlushed = false;
      if (newSize >= autoFlushThreshold) {
        // Auto-flush when threshold is reached - flush ALL events
        const flushedEvents = await this.flushEvents(); // Flush all events, not just a portion
        
        // ✅ CRITICAL FIX: Actually emit the auto-flushed events
        if (this._autoFlushCallback) {
          for (const event of flushedEvents) {
            try {
              await this._autoFlushCallback(event.type, event.data, event.options);
            } catch (error) {
              console.error('Auto-flush emission failed:', error);
            }
          }
        }
        autoFlushed = flushedEvents.length > 0;
      }

      return {
        buffered: added,
        bufferSize: this._eventBuffer.getSize(),
        flushed: autoFlushed,
        timing
      };
    } catch (error) {
      const timing = bufferContext.end();
      this._metricsCollector.recordTiming('bufferError', timing);
      throw error;
    }
  }

  // ✅ CRITICAL FIX: Add auto-flush callback property and setter
  private _autoFlushCallback?: (eventType: string, data: unknown, options: IEmissionOptions) => Promise<void>;

  /**
   * Set the auto-flush callback for automatic event emission
   */
  public setAutoFlushCallback(callback: (eventType: string, data: unknown, options: IEmissionOptions) => Promise<void>): void {
    this._autoFlushCallback = callback;
  }

  /**
   * ✅ RESILIENT TIMING: Flush buffered events with timing measurement and priority support
   */
  public async flushEvents(maxEvents?: number): Promise<IBufferedEvent[]> {
    const flushContext = this._resilientTimer.start();
    
    try {
      this._bufferingMetrics.flushOperations++;

      const eventsToFlush = maxEvents || this._config.maxFlushSize;
      const flushedEvents: IBufferedEvent[] = [];

      // Get all items and sort by priority for priority strategy
      const allItems = this._eventBuffer.getAllItems();
      const sortedEntries = Array.from(allItems.entries()).sort((a, b) => {
        // Sort by priority (higher priority first), then by timestamp (older first)
        const priorityDiff = (b[1].priority || 1) - (a[1].priority || 1);
        if (priorityDiff !== 0) return priorityDiff;
        return a[1].timestamp.getTime() - b[1].timestamp.getTime();
      });

      // Take the first N events based on priority order
      const itemsToFlush = sortedEntries.slice(0, eventsToFlush);

      for (const [key, event] of itemsToFlush) {
        flushedEvents.push(event);
        await this._eventBuffer.removeItem(key);
      }

      const timing = flushContext.end();
      this._metricsCollector.recordTiming('flushOperation', timing);

      // Update metrics
      this._updateFlushMetrics(timing.duration, flushedEvents.length);
      this._bufferingMetrics.currentBufferSize = this._eventBuffer.getSize();
      
      return flushedEvents;
    } catch (error) {
      const timing = flushContext.end();
      this._metricsCollector.recordTiming('flushError', timing);
      throw error;
    }
  }

  /**
   * ✅ CRITICAL FIX: Process buffered events properly
   */
  public async processBufferedEvents(emitCallback: (eventType: string, data: unknown, options: IEmissionOptions) => Promise<void>): Promise<number> {
    const processContext = this._resilientTimer.start();
    
    try {
      const flushedEvents = await this.flushEvents();
      let processedCount = 0;
      
      for (const bufferedEvent of flushedEvents) {
        try {
          await emitCallback(bufferedEvent.type, bufferedEvent.data, bufferedEvent.options);
          processedCount++;
        } catch (error) {
          // Log error but continue processing other events
          console.error('Failed to process buffered event:', error);
        }
      }
      
      const timing = processContext.end();
      this._metricsCollector.recordTiming('processBufferedEvents', timing);
      
      return processedCount;
    } catch (error) {
      const timing = processContext.end();
      this._metricsCollector.recordTiming('processBufferedEventsError', timing);
      throw error;
    }
  }

  /**
   * Get current buffer size
   */
  public getBufferSize(): number {
    return this._eventBuffer.getSize();
  }

  /**
   * Check if buffer is full
   */
  public isBufferFull(): boolean {
    return this._eventBuffer.getSize() >= this._config.bufferSize;
  }

  /**
   * Check if buffer is empty
   */
  public isBufferEmpty(): boolean {
    return this._eventBuffer.getSize() === 0;
  }

  // ============================================================================
  // SECTION 3: BUFFER MANAGEMENT (Lines 201-250)
  // AI Context: "Buffer overflow handling and management"
  // ============================================================================

  private async _handleBufferOverflow(): Promise<void> {
    this._bufferingMetrics.bufferOverflows++;

    // 🔍 PRODUCTION OBSERVABILITY: Enhanced logging for operational monitoring
    this.logInfo('[EventBuffering] Buffer overflow detected', {
      currentSize: this.getBufferSize(),
      maxSize: this._config.bufferSize,
      strategy: this._config.overflowStrategy,
      overflowCount: this._bufferingMetrics.bufferOverflows
    });

    switch (this._config.overflowStrategy) {
      case 'drop':
        // Drop oldest events (handled by circular buffer)
        this.logDebug('[EventBuffering] Overflow strategy: drop - events dropped by circular buffer');
        break;
      case 'flush':
        // 🎯 TARGET LINES 259-261: Flush some events to make room
        this.logInfo('[EventBuffering] Overflow strategy: flush - executing flush', {
          maxFlushSize: this._config.maxFlushSize,
          currentBufferSize: this.getBufferSize()
        });
        await this.flushEvents(this._config.maxFlushSize);
        this.logInfo('[EventBuffering] Overflow flush completed', {
          newBufferSize: this.getBufferSize()
        });
        break;
      case 'expand':
        // Could expand buffer size (not implemented for safety)
        this.logWarning('[EventBuffering] Overflow strategy: expand - not implemented for memory safety');
        break;
    }
  }

  private async _performPeriodicFlush(): Promise<void> {
    // 🔍 PRODUCTION OBSERVABILITY: Enhanced logging for periodic operations
    this.logDebug('[EventBuffering] Periodic flush check initiated', {
      bufferSize: this.getBufferSize(),
      isEmpty: this.isBufferEmpty(),
      flushInterval: this._config.flushIntervalMs
    });

    // 🎯 TARGET LINES 313-315: Conditional flush execution
    if (!this.isBufferEmpty()) {
      this.logInfo('[EventBuffering] Periodic flush executing', {
        bufferSize: this.getBufferSize(),
        maxFlushSize: this._config.maxFlushSize
      });
      await this.flushEvents();
      this.logInfo('[EventBuffering] Periodic flush completed', {
        newBufferSize: this.getBufferSize()
      });
    } else {
      this.logDebug('[EventBuffering] Periodic flush skipped - buffer empty');
    }
  }

  private async _flushAllEvents(): Promise<void> {
    const initialSize = this.getBufferSize();
    let iterationCount = 0;

    // 🔍 PRODUCTION OBSERVABILITY: Enhanced logging for complete flush operations
    this.logInfo('[EventBuffering] Complete flush initiated', {
      initialBufferSize: initialSize,
      maxFlushSize: this._config.maxFlushSize
    });

    // 🎯 TARGET LINES 344-346: Complete buffer flush loop
    while (!this.isBufferEmpty()) {
      const preFlushSize = this.getBufferSize();
      iterationCount++;

      this.logDebug('[EventBuffering] Complete flush iteration', {
        iteration: iterationCount,
        remainingEvents: preFlushSize
      });

      await this.flushEvents();

      const postFlushSize = this.getBufferSize();
      this.logDebug('[EventBuffering] Flush iteration completed', {
        iteration: iterationCount,
        eventsFlushed: preFlushSize - postFlushSize,
        remainingEvents: postFlushSize
      });

      // Safety check to prevent infinite loops
      if (iterationCount > 1000) {
        this.logError('[EventBuffering] Complete flush exceeded maximum iterations', new Error('Flush loop limit exceeded'), {
          iterations: iterationCount,
          remainingEvents: postFlushSize
        });
        break;
      }
    }

    this.logInfo('[EventBuffering] Complete flush finished', {
      initialSize,
      finalSize: this.getBufferSize(),
      totalIterations: iterationCount,
      eventsFlushed: initialSize - this.getBufferSize()
    });
  }

  private _generateBufferId(): string {
    return `buf_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  // ============================================================================
  // SECTION 4: PUBLIC TEST INTERFACE (Lines 401-450)
  // AI Context: "Public methods for comprehensive test coverage and production observability"
  // ============================================================================

  /**
   * 🧪 TEST INTERFACE: Public method for testing buffer overflow scenarios
   * @internal - For testing and observability purposes only
   * @production-safe - Includes comprehensive logging for operational visibility
   */
  public async triggerBufferOverflowTest(): Promise<{
    strategy: string;
    executed: boolean;
    bufferSizeBefore: number;
    bufferSizeAfter: number;
    timing: IResilientTimingResult;
  }> {
    const overflowContext = this._resilientTimer.start();

    try {
      const bufferSizeBefore = this.getBufferSize();

      // 🔍 PRODUCTION OBSERVABILITY: Log overflow trigger for monitoring
      this.logInfo('[EventBuffering] Buffer overflow test triggered', {
        currentSize: bufferSizeBefore,
        maxSize: this._config.bufferSize,
        strategy: this._config.overflowStrategy,
        timestamp: new Date().toISOString()
      });

      // Execute the private overflow handler - TARGET LINES 259-261
      await this._handleBufferOverflow();

      const bufferSizeAfter = this.getBufferSize();
      const timing = overflowContext.end();

      // 🔍 PRODUCTION OBSERVABILITY: Log overflow completion
      this.logInfo('[EventBuffering] Buffer overflow test completed', {
        strategy: this._config.overflowStrategy,
        bufferSizeBefore,
        bufferSizeAfter,
        sizeDifference: bufferSizeBefore - bufferSizeAfter,
        executionTime: timing.duration
      });

      return {
        strategy: this._config.overflowStrategy,
        executed: true,
        bufferSizeBefore,
        bufferSizeAfter,
        timing
      };
    } catch (error) {
      const timing = overflowContext.end();
      this.logError('[EventBuffering] Buffer overflow test failed', error, {
        strategy: this._config.overflowStrategy,
        executionTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * 🧪 TEST INTERFACE: Public method for testing periodic flush mechanism
   * @internal - For testing and observability purposes only
   * @production-safe - Includes comprehensive logging for operational visibility
   */
  public async executePeriodicFlushTest(): Promise<{
    executed: boolean;
    bufferWasEmpty: boolean;
    bufferSizeBefore: number;
    bufferSizeAfter: number;
    eventsFlushed: number;
    timing: IResilientTimingResult;
  }> {
    const periodicContext = this._resilientTimer.start();

    try {
      const bufferSizeBefore = this.getBufferSize();
      const bufferWasEmpty = this.isBufferEmpty();

      // 🔍 PRODUCTION OBSERVABILITY: Log periodic flush trigger
      this.logInfo('[EventBuffering] Periodic flush test triggered', {
        bufferSize: bufferSizeBefore,
        isEmpty: bufferWasEmpty,
        flushInterval: this._config.flushIntervalMs,
        timestamp: new Date().toISOString()
      });

      // Execute the private periodic flush handler - TARGET LINES 313-315
      await this._performPeriodicFlush();

      const bufferSizeAfter = this.getBufferSize();
      const eventsFlushed = bufferSizeBefore - bufferSizeAfter;
      const timing = periodicContext.end();

      // 🔍 PRODUCTION OBSERVABILITY: Log periodic flush completion
      this.logInfo('[EventBuffering] Periodic flush test completed', {
        bufferWasEmpty,
        bufferSizeBefore,
        bufferSizeAfter,
        eventsFlushed,
        executionTime: timing.duration
      });

      return {
        executed: true,
        bufferWasEmpty,
        bufferSizeBefore,
        bufferSizeAfter,
        eventsFlushed,
        timing
      };
    } catch (error) {
      const timing = periodicContext.end();
      this.logError('[EventBuffering] Periodic flush test failed', error, {
        executionTime: timing.duration
      });
      throw error;
    }
  }

  /**
   * 🧪 TEST INTERFACE: Public method for testing complete buffer flush during shutdown
   * @internal - For testing and observability purposes only
   * @production-safe - Includes comprehensive logging for operational visibility
   */
  public async executeCompleteFlushTest(): Promise<{
    executed: boolean;
    initialBufferSize: number;
    totalEventsFlushed: number;
    flushIterations: number;
    timing: IResilientTimingResult;
  }> {
    const completeFlushContext = this._resilientTimer.start();

    try {
      const initialBufferSize = this.getBufferSize();

      // 🔍 PRODUCTION OBSERVABILITY: Log complete flush trigger
      this.logInfo('[EventBuffering] Complete flush test triggered', {
        initialBufferSize,
        maxFlushSize: this._config.maxFlushSize,
        timestamp: new Date().toISOString()
      });

      let flushIterations = 0;

      // Execute the private complete flush handler - TARGET LINES 344-346
      // Track iterations manually for test observability
      while (!this.isBufferEmpty()) {
        flushIterations++;
        await this.flushEvents(this._config.maxFlushSize);

        // Safety check to prevent infinite loops
        if (flushIterations > 100) {
          this.logWarning('[EventBuffering] Complete flush test exceeded max iterations', {
            iterations: flushIterations,
            remainingEvents: this.getBufferSize()
          });
          break;
        }
      }

      const totalEventsFlushed = initialBufferSize - this.getBufferSize();
      const timing = completeFlushContext.end();

      // 🔍 PRODUCTION OBSERVABILITY: Log complete flush completion
      this.logInfo('[EventBuffering] Complete flush test completed', {
        initialBufferSize,
        totalEventsFlushed,
        flushIterations,
        finalBufferSize: this.getBufferSize(),
        executionTime: timing.duration
      });

      return {
        executed: true,
        initialBufferSize,
        totalEventsFlushed,
        flushIterations,
        timing
      };
    } catch (error) {
      const timing = completeFlushContext.end();
      this.logError('[EventBuffering] Complete flush test failed', error, {
        executionTime: timing.duration
      });
      throw error;
    }
  }

  // ============================================================================
  // SECTION 5: METRICS AND MONITORING (Lines 651-700)
  // AI Context: "Buffering metrics and performance monitoring"
  // ============================================================================

  /**
   * Get buffering metrics
   */
  public getBufferingMetrics() {
    return {
      ...this._bufferingMetrics,
      bufferCapacity: this._config.bufferSize,
      bufferUtilization: this._bufferingMetrics.currentBufferSize / this._config.bufferSize,
      metricsSnapshot: this._metricsCollector.createSnapshot()
    };
  }

  /**
   * Reset buffering metrics
   */
  public resetBufferingMetrics(): void {
    this._bufferingMetrics = {
      totalBuffered: 0,
      totalFlushed: 0,
      bufferOverflows: 0,
      flushOperations: 0,
      averageBufferTime: 0,
      averageFlushTime: 0,
      currentBufferSize: this._eventBuffer.getSize()
    };
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private _updateBufferingMetrics(duration: number): void {
    const totalBuffered = this._bufferingMetrics.totalBuffered;
    const currentAverage = this._bufferingMetrics.averageBufferTime;
    
    // Update rolling average
    this._bufferingMetrics.averageBufferTime = 
      (currentAverage * (totalBuffered - 1) + duration) / totalBuffered;
  }

  private _updateFlushMetrics(duration: number, eventsFlushed: number): void {
    const totalFlushOps = this._bufferingMetrics.flushOperations;
    const currentAverage = this._bufferingMetrics.averageFlushTime;

    // Update rolling average
    this._bufferingMetrics.averageFlushTime =
      (currentAverage * (totalFlushOps - 1) + duration) / totalFlushOps;

    this._bufferingMetrics.totalFlushed += eventsFlushed;
  }

  // ============================================================================
  // SECTION 6: LOGGING INTERFACE IMPLEMENTATION (Lines 592-620)
  // AI Context: "ILoggingService interface implementation for production observability"
  // ============================================================================

  /**
   * Log informational message
   */
  public logInfo(message: string, details?: Record<string, unknown>): void {
    this._logger.logInfo(message, details);
  }

  /**
   * Log warning message
   */
  public logWarning(message: string, details?: Record<string, unknown>): void {
    this._logger.logWarning(message, details);
  }

  /**
   * Log error message
   */
  public logError(message: string, error: unknown, details?: Record<string, unknown>): void {
    this._logger.logError(message, error, details);
  }

  /**
   * Log debug message
   */
  public logDebug(message: string, details?: Record<string, unknown>): void {
    this._logger.logDebug(message, details);
  }
}
