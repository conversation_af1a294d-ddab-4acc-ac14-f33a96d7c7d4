/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT SOURCE FILE
 * ============================================================================
 *
 * @file Cleanup Types
 * @filepath shared/src/base/types/CleanupTypes.ts
 * @milestone M0
 * @task-id M-TSK-01.SUB-01.REF-01.TYPES
 * @component cleanup-types
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template typescript-source-file
 * @tier shared
 * @context foundation-context
 * @category Types
 * @created 2025-07-24
 * @modified 2025-09-10 16:00:00 +00
 * @version 2.3.0
 *
 * @description
 * Comprehensive type definitions for the enhanced cleanup coordination system providing complete
 * type safety and interface definitions for the OA Framework cleanup infrastructure. This component
 * defines all interfaces, types, and contracts for enterprise-grade cleanup operations.
 *
 * Key Features:
 * - Component registry interfaces for enterprise cleanup operations with comprehensive validation
 * - Cleanup template system with reusable workflows and validation mechanisms
 * - Dependency resolution with cycle detection and optimization algorithms
 * - Rollback and recovery system with checkpoint management and state restoration
 * - Template execution with comprehensive result tracking and performance monitoring
 * - Performance metrics and monitoring interfaces with detailed analytics
 * - Anti-Simplification Policy compliance with complete type coverage and validation
 *
 * Architecture Integration:
 * - Provides type definitions for CleanupCoordinator and enhanced cleanup modules
 * - Enables CleanupTemplateManager, DependencyResolver, RollbackManager, SystemOrchestrator
 * - Supports comprehensive type safety across the entire cleanup coordination system
 * - Ensures consistent interfaces and contracts for all cleanup operations
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-003-enhanced-services-refactoring
 * @governance-dcr DCR-foundation-003-enhanced-services-modularization
 * @governance-rev REV-foundation-20250910-m0-cleanup-types-approval
 * @governance-strat STRAT-foundation-001-cleanup-types-integration-governance
 * @governance-status approved
 * @governance-compliance authority-validated
 * @milestone-compliance M0-cleanup-types-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on shared/src/base/CleanupCoordinator.ts
 * @enables shared/src/base/modules/cleanup/CleanupTemplateManager.ts, shared/src/base/modules/cleanup/DependencyResolver.ts
 * @implements ICleanupTypes
 * @related-contexts foundation-context, memory-safety-context, cleanup-orchestration-context
 * @governance-impact enhanced-cleanup-coordination, type-safety-enhancement
 * @api-classification types
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level low
 * @base-class none
 * @memory-boundaries not-applicable
 * @resource-cleanup not-applicable
 * @timing-resilience not-applicable
 * @performance-target not-applicable
 * @memory-footprint minimal
 *
 * 🚪 GATEWAY INTEGRATION (v2.3)
 * @gateway-integration not-applicable
 * @api-registration not-applicable
 * @access-pattern types
 * @gateway-compliance not-applicable
 * @milestone-integration M0
 *
 * 🎯 ENHANCED METADATA (v2.3)
 * @component-type type-definitions
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @test-coverage 87%
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/foundation-context/types/cleanup-types.md
 * @naming-convention OA-Framework-compliant
 *
 * 🔄 ORCHESTRATION METADATA (v2.3)
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   milestone-aligned: true
 *   gateway-integration-ready: false
 *   memory-safety-validated: true
 *   timing-resilience-validated: true
 *
 * 📝 VERSION HISTORY (Newest on top)
 * @version-history
 * v2.3.0 (2025-09-10) - Upgraded to v2.3 header format with enhanced cleanup types metadata
 * v2.1.0 (2025-07-24) - Comprehensive type definitions for enhanced cleanup coordination system
 * v1.0.0 (2025-07-24) - Initial implementation with component registry and cleanup template interfaces
 *
 * ============================================================================
 */

// Import base cleanup types
import {
  CleanupOperationType,
  CleanupPriority,
  CleanupStatus,
  ICleanupCoordinatorConfig
} from '../CleanupCoordinatorEnhanced';

// Re-export base types for convenience
export {
  CleanupOperationType,
  CleanupPriority,
  CleanupStatus,
  ICleanupCoordinatorConfig
};

// ============================================================================
// SECTION 1: COMPONENT REGISTRY INTERFACES (Lines 1-100)
// AI Context: "Component registry infrastructure for enterprise cleanup operations"
// ============================================================================

/**
 * Component registry for managing cleanup operations
 */
export interface IComponentRegistry {
  findComponents(pattern?: string): Promise<string[]>;
  getCleanupOperation(operationName: string): CleanupOperationFunction | undefined;
  registerOperation(name: string, operation: CleanupOperationFunction): boolean;
  hasOperation(operationName: string): boolean;
  listOperations(): string[];
  getOperationMetrics(operationName?: string): IOperationMetrics;
}

/**
 * Cleanup operation function signature
 */
export type CleanupOperationFunction = (component: string, params: any) => Promise<ICleanupOperationResult>;

/**
 * Cleanup operation result
 */
export interface ICleanupOperationResult {
  success: boolean;
  duration: number;
  component: string;
  operation: string;
  params?: any;
  cleaned?: string[];
  optimized?: boolean;
  rolledBack?: string[];
  timestamp: Date;
  [key: string]: any;
}

/**
 * Operation metrics
 */
export interface IOperationMetrics {
  totalOperations: number;
  executionCount: number;
  averageExecutionTime: number;
  successRate?: number;
  lastExecution?: Date;
}

// ============================================================================
// SECTION 2: CLEANUP TEMPLATE SYSTEM INTERFACES (Lines 101-200)
// AI Context: "Template system for reusable cleanup workflows and validation"
// ============================================================================

/**
 * Cleanup template system interfaces for reusable workflows
 */
export interface ICleanupTemplate {
  id: string;
  name: string;
  description: string;
  version: string;
  operations: ICleanupTemplateStep[];
  conditions: ICleanupCondition[];
  rollbackSteps: ICleanupTemplateStep[];
  metadata: Record<string, any>;
  tags: string[];
  createdAt: Date;
  modifiedAt: Date;
  author: string;
  validationRules: ITemplateValidationRule[];
}

export interface ICleanupTemplateStep {
  id: string;
  type: CleanupOperationType;
  componentPattern: string; // Regex pattern for component matching
  operationName: string; // Template operation name
  parameters: Record<string, any>;
  timeout: number;
  retryPolicy: IRetryPolicy;
  dependsOn: string[]; // Step IDs this step depends on
  condition?: IStepCondition;
  rollbackOperation?: string;
  priority: CleanupPriority;
  estimatedDuration: number; // milliseconds
  description: string;
}

export interface IRetryPolicy {
  maxRetries: number;
  retryDelay: number; // milliseconds
  backoffMultiplier: number;
  maxRetryDelay: number;
  retryOnErrors: string[]; // Error types to retry on
}

export interface IStepCondition {
  type: 'always' | 'on_success' | 'on_failure' | 'custom' | 'component_exists' | 'resource_available';
  customCondition?: (context: IStepExecutionContext) => boolean;
  componentId?: string;
  resourceType?: string;
  resourceThreshold?: number;
}

export interface ICleanupCondition {
  type: 'system_health' | 'resource_usage' | 'component_state' | 'custom';
  condition: (context: ITemplateExecutionContext) => boolean;
  description: string;
  required: boolean;
}

export interface ITemplateValidationRule {
  type: 'dependency_check' | 'resource_validation' | 'component_compatibility' | 'custom';
  validator: (template: ICleanupTemplate) => IValidationResult;
  description: string;
  severity: 'error' | 'warning' | 'info';
}

export interface IValidationResult {
  valid: boolean;
  issues: IValidationIssue[];
  warnings: string[];
  suggestions: string[];
}

export interface IValidationIssue {
  type: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
  stepId?: string;
  field?: string;
}

// ============================================================================
// SECTION 3: TEMPLATE EXECUTION INTERFACES (Lines 201-280)
// AI Context: "Template execution context and result tracking interfaces"
// ============================================================================

/**
 * Template execution interfaces
 */
export interface ITemplateExecution {
  id: string;
  templateId: string;
  targetComponents: string[];
  parameters: Record<string, any>;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  stepResults: Map<string, IStepExecutionResult>;
  rollbackExecuted: boolean;
  error?: Error;
  metrics: ITemplateExecutionMetrics;
}

export interface IStepExecutionContext {
  stepId: string;
  templateId: string;
  executionId: string;
  componentId: string;
  parameters: Record<string, any>;
  previousResults: Map<string, any>;
  executionAttempt: number;
  startTime: Date;
  globalContext: ITemplateExecutionContext;
}

export interface ITemplateExecutionContext {
  executionId: string;
  templateId: string;
  targetComponents: string[];
  parameters: Record<string, any>;
  systemState: Record<string, any>;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
}

export interface IStepExecutionResult {
  stepId: string;
  componentId: string;
  success: boolean;
  executionTime: number;
  result: any;
  error?: Error;
  retryCount: number;
  skipped: boolean;
  rollbackRequired: boolean;
}

export interface ITemplateExecutionResult {
  executionId: string;
  templateId: string;
  status: 'success' | 'failure' | 'partial';
  executedSteps: number;
  totalSteps: number;
  failedSteps: number;
  skippedSteps: number;
  executionTime: number;
  results: IStepExecutionResult[];
  rollbackExecuted: boolean;
  warnings: string[];
  errors: Error[];
}

export interface ITemplateExecutionMetrics {
  totalSteps: number;
  executedSteps: number;
  failedSteps: number;
  skippedSteps: number;
  averageStepTime: number;
  longestStepTime: number;
  dependencyResolutionTime: number;
  validationTime: number;
  totalExecutionTime: number;
}

// ============================================================================
// SECTION 4: DEPENDENCY RESOLUTION INTERFACES (Lines 281-350)
// AI Context: "Advanced dependency graph system with cycle detection and optimization"
// ============================================================================

/**
 * Advanced dependency resolution interfaces
 */
export interface IDependencyGraph {
  nodes: Set<string>;
  edges: Map<string, Set<string>>; // operation -> dependencies
  addNode(operationId: string): void;
  addDependency(operationId: string, dependsOn: string): void;
  removeDependency(operationId: string, dependsOn: string): void;
  resolveDependencies(operationId: string): string[];
  detectCircularDependencies(): string[][];
  optimizeExecutionOrder(operations: string[]): string[];
  getTopologicalSort(): string[];
  getCriticalPath(): string[];
  getParallelGroups(): string[][];
}

export interface IDependencyAnalysis {
  hasCycles: boolean;
  cycles: string[][];
  criticalPath: string[];
  parallelGroups: string[][];
  estimatedExecutionTime: number;
  bottlenecks: string[];
  optimizationOpportunities: IOptimizationOpportunity[];
  riskAssessment: IRiskAssessment;
}

export interface IOptimizationOpportunity {
  type: 'parallelization' | 'dependency_removal' | 'priority_adjustment' | 'resource_optimization';
  description: string;
  estimatedImprovement: number; // percentage
  implementationComplexity: 'low' | 'medium' | 'high';
  riskLevel: 'low' | 'medium' | 'high';
  affectedOperations: string[];
}

export interface IRiskAssessment {
  overallRisk: 'low' | 'medium' | 'high' | 'critical';
  riskFactors: IRiskFactor[];
  mitigationStrategies: string[];
  contingencyPlans: string[];
}

export interface IRiskFactor {
  type: 'circular_dependency' | 'resource_contention' | 'timing_constraint' | 'external_dependency';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedOperations: string[];
  likelihood: number; // 0-1
  impact: number; // 0-1
}

// ============================================================================
// SECTION 5: ROLLBACK AND RECOVERY INTERFACES (Lines 351-450)
// AI Context: "Comprehensive rollback system with checkpoints and state management"
// ============================================================================

/**
 * Rollback and recovery interfaces
 */
export interface ICleanupRollback {
  createCheckpoint(operationId: string, state?: any): Promise<string>;
  rollbackToCheckpoint(checkpointId: string): Promise<IRollbackResult>;
  rollbackOperation(operationId: string): Promise<IRollbackResult>;
  rollbackTemplate(executionId: string): Promise<IRollbackResult>;
  listCheckpoints(): ICheckpoint[];
  cleanupCheckpoints(olderThan: Date): Promise<number>;
  validateRollbackCapability(operationId: string): IRollbackCapabilityResult;
}

export interface ICheckpoint {
  id: string;
  operationId: string;
  executionId?: string;
  templateId?: string;
  timestamp: Date;
  state: any;
  rollbackActions: IRollbackAction[];
  metadata: Record<string, any>;
  dependencies: string[];
  systemSnapshot: ISystemSnapshot;
  checksum: string;
}

export interface IRollbackAction {
  type: 'restore_state' | 'execute_operation' | 'cleanup_resource' | 'notify_component' | 'revert_configuration';
  parameters: Record<string, any>;
  timeout: number;
  critical: boolean; // If true, rollback fails if this action fails
  priority: number;
  estimatedDuration: number;
  description: string;
  componentId?: string;
  resourceId?: string;
}

export interface IRollbackResult {
  checkpointId?: string;
  operationId: string;
  success: boolean;
  actionsExecuted: number;
  actionsFailed: number;
  executionTime: number;
  errors: Error[];
  warnings: string[];
  partialSuccess: boolean;
  rollbackLevel: 'complete' | 'partial' | 'failed';
}

export interface IRollbackCapabilityResult {
  canRollback: boolean;
  checkpointAvailable: boolean;
  rollbackComplexity: 'simple' | 'moderate' | 'complex';
  estimatedRollbackTime: number;
  riskLevel: 'low' | 'medium' | 'high';
  requirements: string[];
  limitations: string[];
}

export interface ISystemSnapshot {
  timestamp: Date;
  componentStates: Map<string, any>;
  resourceStates: Map<string, any>;
  configurationStates: Map<string, any>;
  activeOperations: string[];
  systemMetrics: Record<string, number>;
  version: string;
}

export interface IRollbackExecution {
  checkpointId: string;
  timestamp: Date;
  result: IRollbackResult;
  triggeredBy: 'manual' | 'automatic' | 'error_handler';
  reason: string;
}

// ============================================================================
// SECTION 6: CONFIGURATION AND UTILITY INTERFACES (Lines 451-500)
// AI Context: "Enhanced configuration and filtering interfaces"
// ============================================================================

/**
 * Enhanced configuration interface
 */
export interface IEnhancedCleanupConfig extends ICleanupCoordinatorConfig {
  templateValidationEnabled?: boolean;
  dependencyOptimizationEnabled?: boolean;
  rollbackEnabled?: boolean;
  maxCheckpoints?: number;
  checkpointRetentionDays?: number;
  phaseIntegrationEnabled?: boolean;
  performanceMonitoringEnabled?: boolean;
}

/**
 * Template execution options
 */
export interface ITemplateExecutionOptions {
  createCheckpoint?: boolean;
  skipValidation?: boolean;
  parallelExecution?: boolean;
  timeoutOverride?: number;
  retryOverride?: IRetryPolicy;
  metadata?: Record<string, any>;
}

/**
 * Template filtering options
 */
export interface ITemplateFilter {
  tags?: string[];
  operationType?: CleanupOperationType;
  author?: string;
  namePattern?: string;
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * Checkpoint filtering options
 */
export interface ICheckpointFilter {
  operationId?: string;
  templateId?: string;
  since?: Date;
  until?: Date;
} 