/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT SOURCE FILE
 * ============================================================================
 *
 * @file Governance Types
 * @filepath shared/src/types/platform/governance/governance-types.ts
 * @milestone M0
 * @task-id M-TSK-01.SUB-04.1.TYP-04
 * @component governance-types
 * @reference foundation-context.TYPES.004
 * @template typescript-source-file
 * @tier shared
 * @context foundation-context
 * @category Type-Definitions
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-09-11 19:05:00 +00
 * @version 2.3.0
 *
 * @description
 * Enterprise-grade governance types module providing comprehensive type definitions
 * for governance system components, rule execution, workflow management, and
 * enterprise governance operations within the OA Framework infrastructure.
 *
 * Key Features:
 * - Comprehensive type definitions for governance system components and infrastructure
 * - Advanced rule execution metrics and status tracking types with performance monitoring
 * - Enterprise governance workflow and process type definitions with automation support
 * - Enterprise-grade governance type structures with comprehensive validation capabilities
 * - Performance-optimized type definitions for governance operations and rule processing
 * - Integration type definitions for governance system coordination and orchestration
 * - Type safety for governance rule execution and monitoring with comprehensive coverage
 * - Comprehensive type coverage for governance functionality across all OA Framework systems
 *
 * Architecture Integration:
 * - Provides foundational type definitions for governance infrastructure and systems
 * - Supports enterprise-grade governance operations with comprehensive type safety
 * - Enables governance rule execution and monitoring with advanced type definitions
 * - Integrates with all OA Framework governance and compliance management systems
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-012-governance-types-architecture
 * @governance-dcr DCR-foundation-012-governance-types-development
 * @governance-rev REV-foundation-20250911-m0-governance-types-approval
 * @governance-strat STRAT-foundation-012-governance-types-integration-governance
 * @governance-status approved
 * @governance-compliance authority-validated
 * @milestone-compliance M0-governance-types-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on shared/src/types/platform/tracking/tracking-types.ts
 * @depends-on shared/src/constants/platform/governance/governance-constants.ts
 * @enables server/src/platform/governance/core-managers/GovernanceManager.ts
 * @enables server/src/platform/governance/automation-processing/GovernanceProcessor.ts
 * @enables server/src/platform/governance/rule-management/RuleExecutionEngine.ts
 * @related-contexts foundation-context, governance-context, type-definitions-context
 * @governance-impact framework-foundation, governance-system, type-safety
 * @api-classification type-definitions
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level not-applicable
 * @base-class not-applicable
 * @memory-boundaries not-applicable
 * @resource-cleanup not-applicable
 * @timing-resilience not-applicable
 * @performance-target not-applicable
 * @memory-footprint not-applicable
 *
 * 🚪 GATEWAY INTEGRATION (v2.3)
 * @gateway-integration not-applicable
 * @api-registration not-applicable
 * @access-pattern direct
 * @gateway-compliance not-applicable
 * @milestone-integration M0
 *
 * 🎯 ENHANCED METADATA (v2.3)
 * @component-type governance-types-definitions
 * @lifecycle-stage implementation
 * @testing-status type-checked, integration-tested
 * @test-coverage not-applicable
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/governance-context/types/governance-types.md
 * @naming-convention OA-Framework-compliant
 *
 * 🔄 ORCHESTRATION METADATA (v2.3)
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   milestone-aligned: true
 *   gateway-integration-ready: false
 *   memory-safety-validated: not-applicable
 *   timing-resilience-validated: not-applicable
 *
 * 📝 VERSION HISTORY (Newest on top)
 * @version-history
 * v2.3.0 (2025-09-11) - Upgraded to v2.3 header format with enhanced governance types metadata
 * v2.1.0 (2025-07-28) - Initial implementation with comprehensive governance type definitions
 *
 * ============================================================================
 */

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES
// AI Context: Governance types dependencies and external type imports
// ============================================================================

import { TTimeRange } from '../tracking/tracking-types';

/**
 * Rule execution metrics type
 */
export type TRuleExecutionMetrics = {
  ruleId: string;
  executionTime: number;
  status: 'success' | 'failure' | 'timeout';
  resourceUsage: {
    cpu: number;
    memory: number;
    network: number;
  };
  timestamp: Date;
  metadata: Record<string, any>;
};

/**
 * Compliance metrics type
 */
export type TComplianceMetrics = {
  ruleId: string;
  complianceScore: number;
  violations: number;
  timestamp: Date;
  metadata: Record<string, any>;
};

/**
 * Rule performance metrics type
 */
export type TRulePerformanceMetrics = {
  ruleId: string;
  timeRange: TTimeRange;
  executionMetrics: {
    averageExecutionTime: number;
    totalExecutions: number;
    executionTimeData: any[];
  };
  resourceMetrics: {
    averageMemoryUsage: number;
    averageCpuUsage: number;
    memoryUsageData: any[];
    cpuUsageData: any[];
  };
  performanceScore: number;
  recommendations: string[];
};

/**
 * System metrics type
 */
export type TSystemMetrics = {
  timeRange: TTimeRange;
  metrics: {
    ruleExecutions: any;
    ruleResults: any;
    complianceScores: any;
    complianceViolations: any;
    memoryUsage: any;
    cpuUsage: any;
    systemHealth: {
      totalDataPointsCollected: number;
      totalAlertsGenerated: number;
      avgCollectionTimeMs: number;
      errorCount: number;
      activeAlerts: number;
    };
  };
  systemScore: number;
  recommendations: string[];
  summary: {
    totalRuleExecutions: number;
    averageExecutionTime: number;
    systemHealthScore: number;
    activeAlertsCount: number;
  };
};

/**
 * Metrics dashboard type
 */
export type TMetricsDashboard = {
  dashboardId: string;
  title: string;
  description: string;
  timeRange: TTimeRange;
  panels: Array<{
    panelId: string;
    title: string;
    type: 'chart' | 'gauge' | 'table' | 'stat';
    metrics: any[];
    configuration: Record<string, any>;
  }>;
  metadata: Record<string, any>;
};

/**
 * Export format type
 */
export type TExportFormat = 'json' | 'csv' | 'excel' | 'pdf';

/**
 * Export result type
 */
export type TExportResult = {
  exportId: string;
  format: TExportFormat;
  url: string;
  expiresAt: Date;
  metadata: Record<string, any>;
};

// ============================================================================
// SECTION 2: INTEGRATION SERVICE TYPES
// AI Context: Integration service types for governance system coordination
// ============================================================================

/**
 * Integration service data type
 */
export type TIntegrationService = {
  serviceId: string;
  serviceName: string;
  serviceVersion: string;
  serviceType: 'bridge' | 'coordinator' | 'validator' | 'monitor';
  serviceStatus: 'active' | 'inactive' | 'degraded' | 'maintenance';
  bridgeConnections: TBridgeConnection[];
  integrationMetrics: TIntegrationMetrics;
  lastHealthCheck: Date;
  serviceMetadata: Record<string, unknown>;
};

/**
 * Bridge connection type
 */
export type TBridgeConnection = {
  connectionId: string;
  sourceSystem: string;
  targetSystem: string;
  connectionType: 'bidirectional' | 'unidirectional';
  status: 'connected' | 'disconnected' | 'error';
  latency: number;
  throughput: number;
  errorRate: number;
  lastSync: Date;
  metadata: Record<string, unknown>;
};

/**
 * Integration metrics type
 */
export type TIntegrationMetrics = {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  averageLatency: number;
  throughput: number;
  errorRate: number;
  uptime: number;
  lastUpdate: Date;
  performanceMetrics: Record<string, number>;
};

/**
 * Governance-Tracking Bridge data type
 */
export type TGovernanceTrackingBridgeData = TIntegrationService & {
  governanceSystemConfig: TGovernanceSystemConfig;
  trackingSystemConfig: TTrackingSystemConfig;
  synchronizationStatus: TSynchronizationStatus;
  eventHandlers: TEventHandler[];
  complianceValidators: TComplianceValidator[];
  bridgeHealth: TBridgeHealthStatus;
  diagnosticsHistory: TDiagnosticsRecord[];
};

/**
 * Cross-Reference Validation Bridge data type
 */
export type TCrossReferenceValidationBridgeData = TIntegrationService & {
  validationSources: TValidationSourceConfig[];
  validationTargets: TValidationTargetConfig[];
  validationRules: TValidationRuleConfig[];
  integritySettings: TIntegritySettings;
  performanceSettings: TPerformanceSettings;
  coordinationSettings: TCoordinationSettings;
  securitySettings: TSecuritySettings;
  validationHistory: TValidationHistoryRecord[];
  crossReferenceCache: TCrossReferenceCache;
  validationMetrics: TValidationBridgeMetrics;
  validationStatus: TValidationBridgeStatus;
};

/**
 * Authority Compliance Monitor Bridge configuration type
 */
export type TAuthorityComplianceMonitorBridgeConfig = {
  bridgeId: string;
  bridgeName: string;
  authoritySources: TAuthoritySourceConfig[];
  complianceTargets: TComplianceTargetConfig[];
  validationRules: TAuthorityValidationRule[];
  monitoringSettings: TComplianceMonitoringSettings;
  escalationSettings: TAuthorityEscalationSettings;
  performanceSettings: TPerformanceSettings;
  securitySettings: TSecuritySettings;
  metadata: Record<string, unknown>;
};

/**
 * Authority source configuration type
 */
export type TAuthoritySourceConfig = {
  sourceId: string;
  sourceType: 'governance' | 'tracking' | 'external';
  endpoints: string[];
  authentication: TAuthenticationConfig;
  authorityLevels: string[];
  validationMethods: string[];
  refreshInterval: number;
  metadata: Record<string, unknown>;
};

/**
 * Compliance target configuration type
 */
export type TComplianceTargetConfig = {
  targetId: string;
  targetType: 'governance' | 'tracking' | 'external';
  endpoints: string[];
  complianceTypes: string[];
  reportingMode: 'realtime' | 'batch' | 'scheduled';
  batchSize?: number;
  scheduleInterval?: number;
  metadata: Record<string, unknown>;
};

/**
 * Authority validation rule type
 */
export type TAuthorityValidationRule = {
  ruleId: string;
  ruleName: string;
  ruleType: 'authority' | 'compliance' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
  validationLogic: 'hierarchical' | 'comprehensive' | 'custom';
  errorThreshold: number;
  enabled: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Compliance monitoring settings type
 */
export type TComplianceMonitoringSettings = {
  enabled: boolean;
  monitoringMode: 'realtime' | 'scheduled' | 'hybrid';
  violationDetection: boolean;
  riskAssessment: boolean;
  trendAnalysis: boolean;
  reportingFrequency: TReportingFrequency;
  metadata: Record<string, unknown>;
};

/**
 * Authority escalation settings type
 */
export type TAuthorityEscalationSettings = {
  enabled: boolean;
  escalationPaths: TAuthorityEscalationPath[];
  approvalWorkflows: boolean;
  timeoutMs: number;
  metadata: Record<string, unknown>;
};

/**
 * Authority escalation path type
 */
export type TAuthorityEscalationPath = {
  pathId: string;
  triggerConditions: string[];
  escalationLevels: string[];
  timeoutMs: number;
  metadata: Record<string, unknown>;
};

/**
 * Reporting frequency type
 */
export type TReportingFrequency = {
  realtime: boolean;
  summary: 'hourly' | 'daily' | 'weekly';
  detailed: 'daily' | 'weekly' | 'monthly';
  metadata: Record<string, unknown>;
};

/**
 * Authentication configuration type
 */
export type TAuthenticationConfig = {
  type: 'oauth2' | 'api-key' | 'jwt' | 'basic' | 'bearer' | 'oauth' | 'apikey';
  credentials: Record<string, unknown>;
  refreshInterval?: number;
  tokenRefreshInterval?: number;
  metadata: Record<string, unknown>;
};

/**
 * Governance system configuration type
 */
export type TGovernanceSystemConfig = {
  systemId: string;
  systemName: string;
  version: string;
  endpoints: TSystemEndpoint[];
  authentication: TAuthenticationConfig;
  rulesSyncInterval: number;
  complianceCheckInterval: number;
  eventSubscriptions: string[];
  metadata: Record<string, unknown>;
};

/**
 * Tracking system configuration type
 */
export type TTrackingSystemConfig = {
  systemId: string;
  systemName: string;
  version: string;
  endpoints: TSystemEndpoint[];
  authentication: TAuthenticationConfig;
  dataSyncInterval: number;
  metricsCollectionInterval: number;
  eventSubscriptions: string[];
  metadata: Record<string, unknown>;
};

/**
 * System endpoint type
 */
export type TSystemEndpoint = {
  endpointId: string;
  name: string;
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  authentication: boolean;
  timeout: number;
  retryPolicy: TRetryPolicy;
  metadata: Record<string, unknown>;
};



/**
 * Retry policy type
 */
export type TRetryPolicy = {
  maxAttempts: number;
  initialDelay: number;
  backoffMultiplier: number;
  maxDelay: number;
  retryableErrors: string[];
};

// ============================================================================
// BRIDGE OPERATION TYPES
// ============================================================================

/**
 * Bridge configuration type
 */
export type TBridgeConfig = {
  bridgeId: string;
  bridgeName: string;
  governanceSystem: TGovernanceSystemConfig;
  trackingSystem: TTrackingSystemConfig;
  synchronizationSettings: TSynchronizationSettings;
  eventHandlingSettings: TEventHandlingSettings;
  healthCheckSettings: THealthCheckSettings;
  diagnosticsSettings: TDiagnosticsSettings;
  metadata: Record<string, unknown>;
};

/**
 * Synchronization settings type
 */
export type TSynchronizationSettings = {
  enabled: boolean;
  interval: number;
  batchSize: number;
  retryPolicy: TRetryPolicy;
  conflictResolution: 'governance-wins' | 'tracking-wins' | 'manual';
  metadata: Record<string, unknown>;
};

/**
 * Event handling settings type
 */
export type TEventHandlingSettings = {
  enabled: boolean;
  eventTypes: string[];
  processingMode: 'sync' | 'async';
  bufferSize: number;
  timeout: number;
  retryPolicy: TRetryPolicy;
  metadata: Record<string, unknown>;
};

/**
 * Health check settings type
 */
export type THealthCheckSettings = {
  enabled: boolean;
  interval: number;
  timeout: number;
  thresholds: THealthThresholds;
  alerting: TAlertingConfig;
  metadata: Record<string, unknown>;
};

/**
 * Diagnostics settings type
 */
export type TDiagnosticsSettings = {
  enabled: boolean;
  level: 'basic' | 'detailed' | 'comprehensive';
  retentionPeriod: number;
  exportEnabled: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Health thresholds type
 */
export type THealthThresholds = {
  latency: number;
  errorRate: number;
  throughput: number;
  uptime: number;
  memoryUsage: number;
  cpuUsage: number;
};

/**
 * Alerting configuration type
 */
export type TAlertingConfig = {
  enabled: boolean;
  channels: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  escalation: TEscalationPolicy;
  metadata: Record<string, unknown>;
};

/**
 * Escalation policy type
 */
export type TEscalationPolicy = {
  enabled: boolean;
  levels: TEscalationLevel[];
  timeout: number;
  metadata: Record<string, unknown>;
};

/**
 * Escalation level type
 */
export type TEscalationLevel = {
  level: number;
  delay: number;
  channels: string[];
  recipients: string[];
  metadata: Record<string, unknown>;
};

// ============================================================================
// BRIDGE RESULT TYPES
// ============================================================================

/**
 * Bridge initialization result type
 */
export type TBridgeInitResult = {
  success: boolean;
  bridgeId: string;
  timestamp: Date;
  governanceConnection: TConnectionStatus;
  trackingConnection: TConnectionStatus;
  errors: TBridgeError[];
  warnings: string[];
  metadata: Record<string, unknown>;
};

/**
 * Connection status type
 */
export type TConnectionStatus = {
  connected: boolean;
  latency: number;
  lastCheck: Date;
  errorCount: number;
  metadata: Record<string, unknown>;
};

/**
 * Bridge error type
 */
export type TBridgeError = {
  errorId: string;
  type: 'connection' | 'authentication' | 'timeout' | 'validation' | 'system';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  context: Record<string, unknown>;
  stackTrace?: string;
};

/**
 * Synchronization result type
 */
export type TSynchronizationResult = {
  success: boolean;
  syncId: string;
  timestamp: Date;
  rulesProcessed: number;
  rulesSuccessful: number;
  rulesFailed: number;
  conflicts: TSyncConflict[];
  errors: TBridgeError[];
  duration: number;
  metadata: Record<string, unknown>;
};

/**
 * Synchronization conflict type
 */
export type TSyncConflict = {
  conflictId: string;
  ruleId: string;
  type: 'version' | 'content' | 'authority' | 'dependency';
  governanceValue: unknown;
  trackingValue: unknown;
  resolution: 'governance-wins' | 'tracking-wins' | 'manual' | 'pending';
  timestamp: Date;
  metadata: Record<string, unknown>;
};

/**
 * Forwarding result type
 */
export type TForwardingResult = {
  success: boolean;
  forwardingId: string;
  timestamp: Date;
  dataSize: number;
  processingTime: number;
  targetSystem: string;
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Compliance validation result type
 */
export type TComplianceValidationResult = {
  success: boolean;
  validationId: string;
  timestamp: Date;
  scope: TValidationScope;
  complianceScore: number;
  violations: TComplianceViolation[];
  recommendations: string[];
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Validation scope type
 */
export type TValidationScope = {
  systems: string[];
  ruleTypes: string[];
  timeRange: TTimeRange;
  includeHistorical: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Compliance violation type
 */
export type TComplianceViolation = {
  violationId: string;
  type: 'rule' | 'process' | 'data' | 'authority' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedSystems: string[];
  remediation: string;
  timestamp: Date;
  metadata: Record<string, unknown>;
};

// ============================================================================
// BRIDGE STATUS AND MONITORING TYPES
// ============================================================================

/**
 * Bridge health status type
 */
export type TBridgeHealthStatus = {
  overall: 'healthy' | 'degraded' | 'unhealthy' | 'critical';
  governanceSystem: TSystemHealth;
  trackingSystem: TSystemHealth;
  bridgeComponents: TComponentHealth[];
  lastCheck: Date;
  uptime: number;
  metrics: TBridgeMetrics;
  alerts: THealthAlert[];
  metadata: Record<string, unknown>;
};

/**
 * System health type
 */
export type TSystemHealth = {
  status: 'healthy' | 'degraded' | 'unhealthy' | 'critical';
  latency: number;
  errorRate: number;
  throughput: number;
  lastResponse: Date;
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Component health type
 */
export type TComponentHealth = {
  componentName: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'critical';
  lastCheck: Date;
  metrics: Record<string, number>;
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Bridge metrics type
 */
export type TBridgeMetrics = {
  operationsPerSecond: number;
  averageLatency: number;
  errorRate: number;
  throughput: number;
  uptime: number;
  memoryUsage: number;
  cpuUsage: number;
  networkUsage: number;
  cacheHitRate: number;
  queueSize: number;
  lastUpdate: Date;
  historicalData: TMetricsDataPoint[];
  metadata: Record<string, unknown>;
};

/**
 * Metrics data point type
 */
export type TMetricsDataPoint = {
  timestamp: Date;
  value: number;
  metric: string;
  metadata: Record<string, unknown>;
};

/**
 * Health alert type
 */
export type THealthAlert = {
  alertId: string;
  type: 'performance' | 'error' | 'availability' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  resolvedAt?: Date;
  metadata: Record<string, unknown>;
};

/**
 * Diagnostics result type
 */
export type TDiagnosticsResult = {
  diagnosticsId: string;
  timestamp: Date;
  level: 'basic' | 'detailed' | 'comprehensive';
  systemChecks: TSystemDiagnostic[];
  performanceAnalysis: TPerformanceAnalysis;
  recommendations: TDiagnosticRecommendation[];
  errors: TBridgeError[];
  duration: number;
  metadata: Record<string, unknown>;
};

/**
 * System diagnostic type
 */
export type TSystemDiagnostic = {
  systemName: string;
  checks: TDiagnosticCheck[];
  overall: 'pass' | 'warning' | 'fail';
  metadata: Record<string, unknown>;
};

/**
 * Diagnostic check type
 */
export type TDiagnosticCheck = {
  checkName: string;
  status: 'pass' | 'warning' | 'fail';
  message: string;
  value?: number;
  threshold?: number;
  metadata: Record<string, unknown>;
};

/**
 * Performance analysis type
 */
export type TPerformanceAnalysis = {
  latencyAnalysis: TLatencyAnalysis;
  throughputAnalysis: TThroughputAnalysis;
  errorAnalysis: TErrorAnalysis;
  resourceAnalysis: TResourceAnalysis;
  recommendations: string[];
  metadata: Record<string, unknown>;
};

/**
 * Latency analysis type
 */
export type TLatencyAnalysis = {
  average: number;
  median: number;
  p95: number;
  p99: number;
  trend: 'improving' | 'stable' | 'degrading';
  metadata: Record<string, unknown>;
};

/**
 * Throughput analysis type
 */
export type TThroughputAnalysis = {
  current: number;
  average: number;
  peak: number;
  trend: 'improving' | 'stable' | 'degrading';
  metadata: Record<string, unknown>;
};

/**
 * Error analysis type
 */
export type TErrorAnalysis = {
  errorRate: number;
  errorTypes: Record<string, number>;
  trend: 'improving' | 'stable' | 'degrading';
  topErrors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Resource analysis type
 */
export type TResourceAnalysis = {
  memoryUsage: number;
  cpuUsage: number;
  networkUsage: number;
  diskUsage: number;
  trends: Record<string, 'improving' | 'stable' | 'degrading'>;
  metadata: Record<string, unknown>;
};

/**
 * Diagnostic recommendation type
 */
export type TDiagnosticRecommendation = {
  type: 'performance' | 'reliability' | 'security' | 'maintenance';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  action: string;
  impact: string;
  metadata: Record<string, unknown>;
};

// ============================================================================
// EVENT HANDLING TYPES
// ============================================================================

/**
 * Event handler type
 */
export type TEventHandler = {
  handlerId: string;
  eventType: string;
  sourceSystem: string;
  targetSystem: string;
  processingMode: 'sync' | 'async';
  enabled: boolean;
  priority: number;
  retryPolicy: TRetryPolicy;
  metadata: Record<string, unknown>;
};

/**
 * Governance event type
 */
export type TGovernanceEvent = {
  eventId: string;
  eventType: string;
  source: string;
  timestamp: Date;
  data: Record<string, unknown>;
  metadata: Record<string, unknown>;
};

/**
 * Tracking event type
 */
export type TTrackingEvent = {
  eventId: string;
  eventType: string;
  source: string;
  timestamp: Date;
  data: Record<string, unknown>;
  metadata: Record<string, unknown>;
};

/**
 * Event handling result type
 */
export type TEventHandlingResult = {
  success: boolean;
  eventId: string;
  handlerId: string;
  timestamp: Date;
  processingTime: number;
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Reset result type
 */
export type TResetResult = {
  success: boolean;
  resetId: string;
  timestamp: Date;
  componentsReset: string[];
  errors: TBridgeError[];
  duration: number;
  metadata: Record<string, unknown>;
};

// ============================================================================
// SUPPORTING TYPES
// ============================================================================

/**
 * Synchronization status type
 */
export type TSynchronizationStatus = {
  enabled: boolean;
  lastSync: Date;
  nextSync: Date;
  syncInProgress: boolean;
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  averageSyncTime: number;
  lastSyncResult: TSynchronizationResult | null;
  metadata: Record<string, unknown>;
};

/**
 * Compliance validator type
 */
export type TComplianceValidator = {
  validatorId: string;
  name: string;
  type: string;
  enabled: boolean;
  rules: string[];
  priority: number;
  metadata: Record<string, unknown>;
};

/**
 * Diagnostics record type
 */
export type TDiagnosticsRecord = {
  recordId: string;
  timestamp: Date;
  level: 'basic' | 'detailed' | 'comprehensive';
  result: TDiagnosticsResult;
  metadata: Record<string, unknown>;
};

// ============================================================================
// INTEGRATION DATA TYPES (from existing framework)
// ============================================================================

/**
 * Integration data type
 */
export type TIntegrationData = {
  dataId: string;
  sourceSystem: string;
  targetSystem: string;
  dataType: string;
  payload: Record<string, unknown>;
  timestamp: Date;
  metadata: Record<string, unknown>;
};

/**
 * Processing result type
 */
export type TProcessingResult = {
  success: boolean;
  processingId: string;
  timestamp: Date;
  processingTime: number;
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Monitoring status type
 */
export type TMonitoringStatus = {
  status: 'active' | 'inactive' | 'degraded' | 'error';
  lastCheck: Date;
  metrics: Record<string, number>;
  alerts: THealthAlert[];
  metadata: Record<string, unknown>;
};

/**
 * Optimization result type
 */
export type TOptimizationResult = {
  success: boolean;
  optimizationId: string;
  timestamp: Date;
  improvements: TOptimizationImprovement[];
  performanceGain: number;
  errors: TBridgeError[];
  metadata: Record<string, unknown>;
};

/**
 * Optimization improvement type
 */
export type TOptimizationImprovement = {
  area: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  beforeValue: number;
  afterValue: number;
  metadata: Record<string, unknown>;
};

// ============================================================================
// CROSS-REFERENCE VALIDATION BRIDGE TYPES
// ============================================================================

/**
 * Validation source configuration type
 */
export type TValidationSourceConfig = {
  sourceId: string;
  sourceType: 'governance' | 'tracking' | 'integration' | 'external';
  endpoints: string[];
  authentication: TAuthenticationConfig;
  validationTypes: string[];
  dataFormats: string[];
  refreshInterval: number;
  timeout: number;
  retryPolicy: TRetryPolicy;
  metadata: Record<string, unknown>;
};

/**
 * Validation target configuration type
 */
export type TValidationTargetConfig = {
  targetId: string;
  targetType: 'governance' | 'tracking' | 'integration' | 'external';
  endpoints: string[];
  resultTypes: string[];
  deliveryMode: 'realtime' | 'batch' | 'scheduled';
  batchSize?: number;
  scheduleInterval?: number;
  metadata: Record<string, unknown>;
};

/**
 * Validation rule configuration type
 */
export type TValidationRuleConfig = {
  ruleId: string;
  ruleName: string;
  ruleType: 'integrity' | 'consistency' | 'performance' | 'security' | 'compliance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  validationLogic: 'basic' | 'comprehensive' | 'enterprise' | 'custom';
  errorThreshold: number;
  enabled: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Integrity settings type
 */
export type TIntegritySettings = {
  checksumValidation: boolean;
  referentialIntegrity: boolean;
  schemaValidation: boolean;
  businessRuleValidation: boolean;
  dataConsistencyChecks: boolean;
  crossSystemValidation: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Performance settings type
 */
export type TPerformanceSettings = {
  maxConcurrentValidations?: number;
  validationTimeoutMs?: number;
  batchSize?: number;
  cacheEnabled?: boolean;
  cacheTTL?: number;
  maxMemoryUsage?: number;
  performanceThresholds?: TPerformanceThresholds;
  maxConcurrentOperations?: number;
  operationTimeoutMs?: number;
  maxConcurrentChecks?: number;
  checkTimeoutMs?: number;
  metadata: Record<string, unknown>;
};

/**
 * Performance thresholds type
 */
export type TPerformanceThresholds = {
  maxResponseTime: number;
  maxErrorRate: number;
  maxMemoryUsage: number;
  maxCpuUsage: number;
  metadata: Record<string, unknown>;
};

/**
 * Coordination settings type
 */
export type TCoordinationSettings = {
  enabled: boolean;
  coordinationMode: 'realtime' | 'batch' | 'scheduled';
  conflictResolution: 'priority-based' | 'timestamp-based' | 'manual';
  resultAggregation: boolean;
  distributedValidation: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Security settings type
 */
export type TSecuritySettings = {
  encryptionEnabled: boolean;
  authenticationRequired?: boolean;
  authorizationLevel?: 'basic' | 'elevated' | 'administrative';
  auditLogging?: boolean;
  auditingEnabled?: boolean;
  accessControl: TAccessControlConfig | string;
  authorizationLevels?: string[];
  ipWhitelist?: string[];
  rateLimiting?: boolean;
  metadata: Record<string, unknown>;
};

/**
 * Access control configuration type
 */
export type TAccessControlConfig = {
  allowedRoles: string[];
  restrictedOperations: string[];
  ipWhitelist: string[];
  rateLimiting: TRateLimitConfig;
  metadata: Record<string, unknown>;
};

/**
 * Rate limit configuration type
 */
export type TRateLimitConfig = {
  requestsPerMinute: number;
  burstLimit: number;
  windowSize: number;
  metadata: Record<string, unknown>;
};

/**
 * Validation history record type
 */
export type TValidationHistoryRecord = {
  recordId: string;
  validationId: string;
  timestamp: Date;
  validationType: string;
  componentId: string;
  result: 'success' | 'failure' | 'warning';
  executionTime: number;
  errorCount: number;
  warningCount: number;
  metadata: Record<string, unknown>;
};

/**
 * Cross-reference cache type
 */
export type TCrossReferenceCache = {
  cacheId: string;
  entries: TCrossReferenceCacheEntry[];
  maxSize: number;
  currentSize: number;
  hitRate: number;
  lastCleanup: Date;
  metadata: Record<string, unknown>;
};

/**
 * Cross-reference cache entry type
 */
export type TCrossReferenceCacheEntry = {
  entryId: string;
  componentId: string;
  references: any[];
  validationResult: any;
  timestamp: Date;
  ttl: number;
  accessCount: number;
  metadata: Record<string, unknown>;
};

/**
 * Validation bridge metrics type
 */
export type TValidationBridgeMetrics = {
  totalValidations: number;
  successfulValidations: number;
  failedValidations: number;
  averageExecutionTime: number;
  errorRate: number;
  throughput: number;
  cacheHitRate: number;
  memoryUsage: number;
  cpuUsage: number;
  lastUpdate: Date;
  performanceMetrics: Record<string, number>;
  metadata: Record<string, unknown>;
};

/**
 * Validation bridge status type
 */
export type TValidationBridgeStatus = {
  overall: 'healthy' | 'degraded' | 'unhealthy' | 'critical';
  validationSources: TValidationSourceStatus[];
  validationTargets: TValidationTargetStatus[];
  coordinationStatus: 'active' | 'inactive' | 'degraded';
  lastHealthCheck: Date;
  uptime: number;
  activeValidations: number;
  queueSize: number;
  alerts: TValidationAlert[];
  metadata: Record<string, unknown>;
};

/**
 * Validation source status type
 */
export type TValidationSourceStatus = {
  sourceId: string;
  status: 'connected' | 'disconnected' | 'error' | 'degraded';
  latency: number;
  errorRate: number;
  lastCheck: Date;
  metadata: Record<string, unknown>;
};

/**
 * Validation target status type
 */
export type TValidationTargetStatus = {
  targetId: string;
  status: 'connected' | 'disconnected' | 'error' | 'degraded';
  deliveryRate: number;
  errorRate: number;
  lastDelivery: Date;
  metadata: Record<string, unknown>;
};

/**
 * Validation alert type
 */
export type TValidationAlert = {
  alertId: string;
  alertType: 'error' | 'warning' | 'info';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  component: string;
  timestamp: Date;
  acknowledged: boolean;
  metadata: Record<string, unknown>;
};